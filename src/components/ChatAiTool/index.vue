<!--
 * @Author: songjingyang <EMAIL>
 * @Date: 2025-05-15 16:29:02
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-08-01 16:00:09
 * @FilePath: /mb-users-magic-brush/src/components/ChatAiTool/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai-tool" @click="close" v-if="filteredTools.length">
    <div class="content" @click.stop>
      <div class="tools">
        <div @click="chooseTool(item.functionName)" class="item" v-for="item in filteredTools" :key="item.functionName">
          <span>{{ item.functionName }}</span>
          <span class="tips">&emsp;{{ item.tips }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useChatStore from '../../store/modules/chat'
import { ref, computed, onMounted } from 'vue'

const chatStore = useChatStore()

defineOptions({
  name: 'ChatAiTool',
})

const chooseTool = (tool: string) => {
  chatStore.tool = tool
  close()
}

const close = () => {
  chatStore.setShowAiTool(false)
}

const filteredTools = computed(() => {
  const tools = chatStore.tools?.flatMap(item => item.toolList)
  return tools.filter(item => item.functionName.includes(chatStore.filterKeyword))
})
</script>

<style lang="scss" scoped>
.ai-tool {
  position: absolute;
  bottom: 120%;
  left: 0;
  width: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;

  .content {
    width: 100%;
    background: #ffffff;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    border-radius: 15px;
    max-height: 50%;
    overflow-y: auto;
    overflow-x: hidden;

    .tools {
      margin: 0 25px;
      margin-top: 25px;

      .item {
        height: 40px;
        margin-bottom: 10px;
        font-size: 18px;
        color: #2266ff;

        img {
          height: 22px;
          margin-right: 17px;
        }

        .title {
          font-weight: normal;
          font-size: 20px;
          color: #000000;
        }

        .tips {
          color: #909090;
        }
      }
    }
  }
}
</style>
